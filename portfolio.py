"""Portfolio management for backtesting."""

from __future__ import annotations

from dataclasses import dataclass
from typing import List, Optional, Dict, Any
from enum import Enum
import pandas as pd
import numpy as np


class PositionType(Enum):
    """Position types."""
    LONG = "LONG"
    SHORT = "SHORT"
    FLAT = "FLAT"


@dataclass
class Trade:
    """Represents a completed trade."""
    
    entry_time: pd.Timestamp
    exit_time: pd.Timestamp
    position_type: PositionType
    entry_price: float
    exit_price: float
    quantity: float
    commission: float
    slippage: float
    pnl: float
    pnl_pct: float
    holding_periods: int
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert trade to dictionary."""
        return {
            "entry_time": str(self.entry_time),
            "exit_time": str(self.exit_time),
            "position_type": self.position_type.value,
            "entry_price": self.entry_price,
            "exit_price": self.exit_price,
            "quantity": self.quantity,
            "commission": self.commission,
            "slippage": self.slippage,
            "pnl": self.pnl,
            "pnl_pct": self.pnl_pct,
            "holding_periods": self.holding_periods,
        }


@dataclass
class Position:
    """Represents an open position."""
    
    position_type: PositionType
    entry_time: pd.Timestamp
    entry_price: float
    quantity: float
    commission_paid: float
    slippage_paid: float
    holding_periods: int = 0
    
    def update_holding_periods(self) -> None:
        """Increment holding periods counter."""
        self.holding_periods += 1
    
    def get_unrealized_pnl(self, current_price: float) -> float:
        """Calculate unrealized P&L."""
        if self.position_type == PositionType.LONG:
            return (current_price - self.entry_price) * self.quantity
        elif self.position_type == PositionType.SHORT:
            return (self.entry_price - current_price) * self.quantity
        return 0.0
    
    def get_unrealized_pnl_pct(self, current_price: float) -> float:
        """Calculate unrealized P&L percentage."""
        if self.position_type == PositionType.LONG:
            return (current_price - self.entry_price) / self.entry_price
        elif self.position_type == PositionType.SHORT:
            return (self.entry_price - current_price) / self.entry_price
        return 0.0


class Portfolio:
    """Portfolio management for backtesting."""
    
    def __init__(self, initial_capital: float):
        self.initial_capital = initial_capital
        self.cash = initial_capital
        self.position: Optional[Position] = None
        self.trades: List[Trade] = []
        self.equity_curve: List[float] = [initial_capital]
        self.timestamps: List[pd.Timestamp] = []
        
    def get_total_value(self, current_price: float) -> float:
        """Get total portfolio value."""
        if self.position is None:
            return self.cash
        
        unrealized_pnl = self.position.get_unrealized_pnl(current_price)
        return self.cash + unrealized_pnl
    
    def get_position_value(self, current_price: float) -> float:
        """Get current position value."""
        if self.position is None:
            return 0.0
        return abs(self.position.quantity * current_price)
    
    def can_open_position(self, position_type: PositionType, price: float, 
                         position_size_pct: float, max_position_size: float) -> bool:
        """Check if we can open a new position."""
        if self.position is not None:
            return False
        
        required_capital = self.cash * position_size_pct
        max_allowed = self.get_total_value(price) * max_position_size
        
        return required_capital <= max_allowed and required_capital > 0
    
    def open_position(self, position_type: PositionType, timestamp: pd.Timestamp,
                     price: float, position_size_pct: float, commission_pct: float,
                     slippage_pct: float) -> bool:
        """Open a new position."""
        if self.position is not None:
            return False
        
        # Calculate position size
        available_capital = self.cash * position_size_pct
        
        # Apply slippage
        if position_type == PositionType.LONG:
            execution_price = price * (1 + slippage_pct)
        else:  # SHORT
            execution_price = price * (1 - slippage_pct)
        
        # Calculate quantity
        quantity = available_capital / execution_price
        
        # Calculate costs
        commission = available_capital * commission_pct
        slippage_cost = available_capital * slippage_pct
        
        # Update cash
        self.cash -= (available_capital + commission)
        
        # Create position
        self.position = Position(
            position_type=position_type,
            entry_time=timestamp,
            entry_price=execution_price,
            quantity=quantity,
            commission_paid=commission,
            slippage_paid=slippage_cost
        )
        
        return True
    
    def close_position(self, timestamp: pd.Timestamp, price: float,
                      commission_pct: float, slippage_pct: float) -> Optional[Trade]:
        """Close the current position."""
        if self.position is None:
            return None
        
        # Apply slippage
        if self.position.position_type == PositionType.LONG:
            execution_price = price * (1 - slippage_pct)
        else:  # SHORT
            execution_price = price * (1 + slippage_pct)
        
        # Calculate proceeds
        proceeds = self.position.quantity * execution_price
        commission = proceeds * commission_pct
        slippage_cost = proceeds * slippage_pct
        
        # Calculate P&L
        if self.position.position_type == PositionType.LONG:
            pnl = proceeds - (self.position.quantity * self.position.entry_price)
        else:  # SHORT
            pnl = (self.position.quantity * self.position.entry_price) - proceeds
        
        # Subtract all costs
        total_commission = self.position.commission_paid + commission
        total_slippage = self.position.slippage_paid + slippage_cost
        net_pnl = pnl - total_commission - total_slippage
        
        # Calculate percentage return
        initial_investment = self.position.quantity * self.position.entry_price
        pnl_pct = net_pnl / initial_investment if initial_investment > 0 else 0
        
        # Create trade record
        trade = Trade(
            entry_time=self.position.entry_time,
            exit_time=timestamp,
            position_type=self.position.position_type,
            entry_price=self.position.entry_price,
            exit_price=execution_price,
            quantity=self.position.quantity,
            commission=total_commission,
            slippage=total_slippage,
            pnl=net_pnl,
            pnl_pct=pnl_pct,
            holding_periods=self.position.holding_periods
        )
        
        # Update cash
        self.cash += proceeds - commission
        
        # Store trade and clear position
        self.trades.append(trade)
        self.position = None
        
        return trade
    
    def update_equity_curve(self, timestamp: pd.Timestamp, current_price: float) -> None:
        """Update equity curve with current portfolio value."""
        total_value = self.get_total_value(current_price)
        self.equity_curve.append(total_value)
        self.timestamps.append(timestamp)
        
        # Update holding periods if we have a position
        if self.position is not None:
            self.position.update_holding_periods()
    
    def should_stop_loss(self, current_price: float, stop_loss_pct: Optional[float]) -> bool:
        """Check if position should be closed due to stop loss."""
        if self.position is None or stop_loss_pct is None:
            return False
        
        pnl_pct = self.position.get_unrealized_pnl_pct(current_price)
        return pnl_pct <= -abs(stop_loss_pct)
    
    def should_take_profit(self, current_price: float, take_profit_pct: Optional[float]) -> bool:
        """Check if position should be closed due to take profit."""
        if self.position is None or take_profit_pct is None:
            return False
        
        pnl_pct = self.position.get_unrealized_pnl_pct(current_price)
        return pnl_pct >= abs(take_profit_pct)
    
    def should_close_max_holding(self, max_holding_periods: Optional[int]) -> bool:
        """Check if position should be closed due to max holding periods."""
        if self.position is None or max_holding_periods is None:
            return False
        
        return self.position.holding_periods >= max_holding_periods
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """Get portfolio summary statistics."""
        if not self.trades:
            return {
                "total_trades": 0,
                "winning_trades": 0,
                "losing_trades": 0,
                "win_rate": 0.0,
                "avg_win": 0.0,
                "avg_loss": 0.0,
                "largest_win": 0.0,
                "largest_loss": 0.0,
                "profit_factor": 0.0,
            }
        
        winning_trades = [t for t in self.trades if t.pnl > 0]
        losing_trades = [t for t in self.trades if t.pnl < 0]
        
        total_wins = sum(t.pnl for t in winning_trades)
        total_losses = abs(sum(t.pnl for t in losing_trades))
        
        return {
            "total_trades": len(self.trades),
            "winning_trades": len(winning_trades),
            "losing_trades": len(losing_trades),
            "win_rate": len(winning_trades) / len(self.trades) if self.trades else 0,
            "avg_win": total_wins / len(winning_trades) if winning_trades else 0,
            "avg_loss": total_losses / len(losing_trades) if losing_trades else 0,
            "largest_win": max((t.pnl for t in winning_trades), default=0),
            "largest_loss": min((t.pnl for t in losing_trades), default=0),
            "profit_factor": total_wins / total_losses if total_losses > 0 else float('inf'),
        }
