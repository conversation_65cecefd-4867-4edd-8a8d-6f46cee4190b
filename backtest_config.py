"""Configuration system for backtesting parameters and trading rules."""

from __future__ import annotations

from dataclasses import dataclass, field
from typing import Dict, Any, Optional
from pathlib import Path
import json


@dataclass
class TradingConfig:
    """Trading strategy configuration."""
    
    # Signal thresholds
    long_threshold: float = 0.005  # 0.5% predicted price increase
    short_threshold: float = -0.005  # 0.5% predicted price decrease
    
    # Position sizing
    position_size_pct: float = 0.95  # Use 95% of available capital per trade
    max_position_size: float = 1.0  # Maximum position as fraction of portfolio
    
    # Risk management
    stop_loss_pct: Optional[float] = None  # Stop loss percentage (None = no stop loss)
    take_profit_pct: Optional[float] = None  # Take profit percentage (None = no take profit)
    max_holding_periods: Optional[int] = None  # Max periods to hold position (None = no limit)
    
    # Trading costs
    commission_pct: float = 0.001  # 0.1% commission per trade
    slippage_pct: float = 0.0005  # 0.05% slippage per trade


@dataclass
class BacktestConfig:
    """Backtesting configuration."""
    
    # Data settings
    data_file: str = "data/BTCUSDT_4h.csv"
    start_date: Optional[str] = None  # Format: "YYYY-MM-DD" or None for all data
    end_date: Optional[str] = None  # Format: "YYYY-MM-DD" or None for all data
    
    # Model settings
    model_path: str = "models"
    sequence_length: int = 60  # Must match training config
    
    # Portfolio settings
    initial_capital: float = 10000.0  # Starting capital in USD
    
    # Trading configuration
    trading: TradingConfig = field(default_factory=TradingConfig)
    
    # Output settings
    save_trades: bool = True
    save_equity_curve: bool = True
    output_dir: str = "backtest_results"
    
    # Performance settings
    benchmark_symbol: Optional[str] = None  # For comparison (e.g., "SPY")
    risk_free_rate: float = 0.02  # Annual risk-free rate for Sharpe ratio


@dataclass
class BacktestResults:
    """Container for backtesting results."""
    
    # Performance metrics
    total_return: float
    annual_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    
    # Trade statistics
    total_trades: int
    winning_trades: int
    losing_trades: int
    avg_win: float
    avg_loss: float
    largest_win: float
    largest_loss: float
    
    # Portfolio metrics
    final_capital: float
    max_capital: float
    min_capital: float
    
    # Additional data
    equity_curve: list
    trades: list
    daily_returns: list
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert results to dictionary for JSON serialization."""
        return {
            "performance_metrics": {
                "total_return": self.total_return,
                "annual_return": self.annual_return,
                "sharpe_ratio": self.sharpe_ratio,
                "max_drawdown": self.max_drawdown,
                "win_rate": self.win_rate,
                "profit_factor": self.profit_factor,
            },
            "trade_statistics": {
                "total_trades": self.total_trades,
                "winning_trades": self.winning_trades,
                "losing_trades": self.losing_trades,
                "avg_win": self.avg_win,
                "avg_loss": self.avg_loss,
                "largest_win": self.largest_win,
                "largest_loss": self.largest_loss,
            },
            "portfolio_metrics": {
                "initial_capital": self.equity_curve[0] if self.equity_curve else 0,
                "final_capital": self.final_capital,
                "max_capital": self.max_capital,
                "min_capital": self.min_capital,
            }
        }


def load_config(config_path: str) -> BacktestConfig:
    """Load backtesting configuration from JSON file."""
    config_file = Path(config_path)
    if not config_file.exists():
        raise FileNotFoundError(f"Config file not found: {config_path}")
    
    with open(config_file, 'r') as f:
        config_data = json.load(f)
    
    # Handle nested trading config
    trading_data = config_data.pop('trading', {})
    trading_config = TradingConfig(**trading_data)
    
    return BacktestConfig(trading=trading_config, **config_data)


def save_config(config: BacktestConfig, config_path: str) -> None:
    """Save backtesting configuration to JSON file."""
    config_file = Path(config_path)
    config_file.parent.mkdir(parents=True, exist_ok=True)
    
    # Convert to dictionary
    config_dict = {
        "data_file": config.data_file,
        "start_date": config.start_date,
        "end_date": config.end_date,
        "model_path": config.model_path,
        "sequence_length": config.sequence_length,
        "initial_capital": config.initial_capital,
        "save_trades": config.save_trades,
        "save_equity_curve": config.save_equity_curve,
        "output_dir": config.output_dir,
        "benchmark_symbol": config.benchmark_symbol,
        "risk_free_rate": config.risk_free_rate,
        "trading": {
            "long_threshold": config.trading.long_threshold,
            "short_threshold": config.trading.short_threshold,
            "position_size_pct": config.trading.position_size_pct,
            "max_position_size": config.trading.max_position_size,
            "stop_loss_pct": config.trading.stop_loss_pct,
            "take_profit_pct": config.trading.take_profit_pct,
            "max_holding_periods": config.trading.max_holding_periods,
            "commission_pct": config.trading.commission_pct,
            "slippage_pct": config.trading.slippage_pct,
        }
    }
    
    with open(config_file, 'w') as f:
        json.dump(config_dict, f, indent=2)


def create_default_config(output_path: str = "backtest_config.json") -> BacktestConfig:
    """Create and save a default configuration file."""
    config = BacktestConfig()
    save_config(config, output_path)
    print(f"Created default config at: {output_path}")
    return config


if __name__ == "__main__":
    # Create a default configuration file
    create_default_config()
