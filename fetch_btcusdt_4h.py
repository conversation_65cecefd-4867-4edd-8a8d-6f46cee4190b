from __future__ import annotations

import csv
import time
from datetime import datetime, timezone
from pathlib import Path
from typing import Iterable, List, Optional

import pandas as pd
import requests


BINANCE_API_BASE_URL = "https://api.binance.com"


def fetch_all_klines(
    symbol: str,
    interval: str,
    limit_per_request: int = 1000,
    start_time_ms: int = 0,
    end_time_ms: Optional[int] = None,
    request_pause_seconds: float = 0.25,
) -> List[List]:
    """Fetch all available klines for a symbol/interval from Binance public API.

    Args:
        symbol: Trading pair, e.g. "BTCUSDT".
        interval: Kline interval, e.g. "4h".
        limit_per_request: Max klines per call (Binance allows up to 1000).
        start_time_ms: Epoch milliseconds to start from. Use 0 for earliest.
        end_time_ms: Epoch milliseconds to stop at. None means now.
        request_pause_seconds: Pause between requests to avoid rate limits.

    Returns:
        A list of raw kline rows as returned by Binance.
    """

    all_rows: List[List] = []
    session = requests.Session()

    params = {
        "symbol": symbol.upper(),
        "interval": interval,
        "limit": limit_per_request,
    }

    current_start = int(start_time_ms)
    if end_time_ms is None:
        end_time_ms = int(time.time() * 1000)

    while True:
        params["startTime"] = current_start
        params["endTime"] = end_time_ms

        for attempt in range(5):
            try:
                resp = session.get(f"{BINANCE_API_BASE_URL}/api/v3/klines", params=params, timeout=30)
                if resp.status_code == 429:
                    # Rate limited; back off and retry
                    time.sleep(1.0 + attempt)
                    continue
                resp.raise_for_status()
                batch: List[List] = resp.json()
                break
            except requests.RequestException:
                if attempt == 4:
                    raise
                time.sleep(1.0 + attempt)
        
        if not batch:
            break

        all_rows.extend(batch)

        # Advance start time to just after the last returned kline's close time
        last_close_time = int(batch[-1][6])
        next_start = last_close_time + 1

        # If no progress, stop to avoid infinite loop
        if next_start <= current_start:
            break
        current_start = next_start

        # Pause to be gentle on the API
        time.sleep(request_pause_seconds)

        # If we've reached or surpassed end_time_ms, stop
        if current_start >= end_time_ms:
            break

    return all_rows


def klines_to_dataframe(rows: Iterable[List]) -> pd.DataFrame:
    """Convert raw kline rows to a typed pandas DataFrame with proper columns."""
    cols = [
        "open_time",  # 0
        "open",       # 1
        "high",       # 2
        "low",        # 3
        "close",      # 4
        "volume",     # 5
        "close_time", # 6
        "quote_volume", # 7
        "num_trades",   # 8
        "taker_buy_base_volume",  # 9
        "taker_buy_quote_volume", # 10
        "ignore",                 # 11
    ]
    df = pd.DataFrame(rows, columns=cols)

    # Enforce numeric types
    numeric_cols = ["open", "high", "low", "close", "volume", "quote_volume",
                    "taker_buy_base_volume", "taker_buy_quote_volume"]
    for c in numeric_cols:
        df[c] = pd.to_numeric(df[c], errors="coerce")

    df["open_time"] = pd.to_datetime(df["open_time"], unit="ms", utc=True)
    df["close_time"] = pd.to_datetime(df["close_time"], unit="ms", utc=True)
    df.sort_values("open_time", inplace=True)
    df.reset_index(drop=True, inplace=True)
    return df


def compute_rsi(close: pd.Series, period: int = 14) -> pd.Series:
    """Compute RSI using Wilder's smoothing (EMA with alpha=1/period)."""
    delta = close.diff()
    gains = delta.clip(lower=0)
    losses = -delta.clip(upper=0)
    avg_gain = gains.ewm(alpha=1.0 / period, adjust=False).mean()
    avg_loss = losses.ewm(alpha=1.0 / period, adjust=False).mean()
    rs = avg_gain / avg_loss
    rsi = 100.0 - (100.0 / (1.0 + rs))
    return rsi


def compute_macd(close: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> pd.DataFrame:
    """Compute MACD (fast EMA - slow EMA), signal line, and histogram."""
    ema_fast = close.ewm(span=fast, adjust=False).mean()
    ema_slow = close.ewm(span=slow, adjust=False).mean()
    macd_line = ema_fast - ema_slow
    signal_line = macd_line.ewm(span=signal, adjust=False).mean()
    histogram = macd_line - signal_line
    return pd.DataFrame({
        "macd": macd_line,
        "macd_signal": signal_line,
        "macd_hist": histogram,
    })


def add_indicators(df: pd.DataFrame) -> pd.DataFrame:
    """Add RSI(14) and MACD(12,26,9) to the DataFrame."""
    df = df.copy()
    df["rsi_14"] = compute_rsi(df["close"], period=14)
    macd = compute_macd(df["close"], fast=12, slow=26, signal=9)
    df = pd.concat([df, macd], axis=1)
    return df


def save_to_csv(df: pd.DataFrame, output_path: Path) -> None:
    """Save selected columns to CSV with ISO timestamp."""
    output_path.parent.mkdir(parents=True, exist_ok=True)
    export_df = pd.DataFrame({
        "timestamp": df["open_time"].dt.tz_convert(timezone.utc).dt.strftime("%Y-%m-%d %H:%M:%S%z"),
        "open": df["open"],
        "high": df["high"],
        "low": df["low"],
        "close": df["close"],
        "volume": df["volume"],
        "rsi_14": df["rsi_14"],
        "macd": df["macd"],
        "macd_signal": df["macd_signal"],
        "macd_hist": df["macd_hist"],
    })
    export_df.to_csv(output_path, index=False)


def main() -> None:
    symbol = "BTCUSDT"
    interval = "4h"
    rows = fetch_all_klines(symbol=symbol, interval=interval)
    df = klines_to_dataframe(rows)
    df = add_indicators(df)
    output_csv = Path("data") / f"{symbol}_{interval}.csv"
    save_to_csv(df, output_csv)
    print(f"Saved {len(df)} rows to {output_csv}")


if __name__ == "__main__":
    main()


