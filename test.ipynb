import pandas as pd
import pandas as pd
from sklearn.preprocessing import MinMaxScaler
import numpy as np


df = pd.read_csv("data/BTCUSDT_4h.csv")

df.head()

df.dtypes

df['timestamp'] = pd.to_datetime(df['timestamp'])

# Set the timestamp column as the DataFrame's index
df.set_index('timestamp', inplace=True)

print("DataFrame after loading and converting timestamp:")
print(df.head())

# Drop the first row with missing 'rsi_14' data, as it's not useful for our model
df = df.iloc[1:]

print("DataFrame after dropping the first row:")
print(df.head())

from sklearn.preprocessing import MinMaxScaler

# Separate the features (inputs) and the target (output)
features = df.drop(columns=['close'])
target = df[['close']]

# Initialize the scalers for features and target
features_scaler = MinMaxScaler(feature_range=(0, 1))
target_scaler = MinMaxScaler(feature_range=(0, 1))

# Fit and transform the data
scaled_features = features_scaler.fit_transform(features)
scaled_target = target_scaler.fit_transform(target)

# Create a single DataFrame with all scaled data
scaled_data = pd.DataFrame(scaled_features, columns=features.columns, index=features.index)
scaled_data['close'] = scaled_target

print("Scaled data after normalization:")
print(scaled_data.head())

# Define the function to create sequences
def create_sequences(data, sequence_length):
    X, y = [], []
    for i in range(len(data) - sequence_length):
        X.append(data.iloc[i : i + sequence_length, :].values)
        y.append(data.iloc[i + sequence_length]['close'])
    return np.array(X), np.array(y)

# Set the sequence length
sequence_length = 60

# Create the sequences from our scaled data
X, y = create_sequences(scaled_data, sequence_length)

print(f"Shape of X (Input Sequences): {X.shape}")
print(f"Shape of y (Target Values): {y.shape}")

scaled_data.head()

# Define the training size (e.g., 80% of the data)
train_size = int(len(X) * 0.8)

# Split the data
X_train, X_test = X[:train_size], X[train_size:]
y_train, y_test = y[:train_size], y[train_size:]

print("Final shapes of our training and testing data:")
print(f"Shape of X_train: {X_train.shape}")
print(f"Shape of y_train: {y_train.shape}")
print(f"Shape of X_test: {X_test.shape}")
print(f"Shape of y_test: {y_test.shape}")

import torch
from torch.utils.data import TensorDataset, DataLoader

# Convert the NumPy arrays to PyTorch Tensors
X_train_tensor = torch.from_numpy(X_train).float()
y_train_tensor = torch.from_numpy(y_train).float().unsqueeze(1) # unsqueeze(1) adds a dimension for the target

X_test_tensor = torch.from_numpy(X_test).float()
y_test_tensor = torch.from_numpy(y_test).float().unsqueeze(1)

# Create a TensorDataset for both training and testing data
train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
test_dataset = TensorDataset(X_test_tensor, y_test_tensor)

# Create a DataLoader for each dataset to handle batching
batch_size = 32
train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

print("Data successfully prepared for PyTorch.")

import torch.nn as nn

class LSTM_Model(nn.Module):
    def __init__(self, input_size, hidden_size, num_layers, output_size):
        super(LSTM_Model, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # The LSTM layer itself
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True)
        
        # The final output layer
        self.fc = nn.Linear(hidden_size, output_size)
    
    def forward(self, x):
        # Initialize hidden and cell states
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)
        
        # Pass the input through the LSTM layer
        out, _ = self.lstm(x, (h0, c0))
        
        # Pass the output of the last time step through the fully connected layer
        out = self.fc(out[:, -1, :])
        return out

# Define the model parameters
input_size = X_train.shape[2] # Number of features
hidden_size = 100
num_layers = 2
output_size = 1

# Initialize the model
model = LSTM_Model(input_size, hidden_size, num_layers, output_size)

print("LSTM model architecture:")
print(model)

# Define loss function and optimizer
criterion = nn.MSELoss()
optimizer = torch.optim.Adam(model.parameters(), lr=0.001)

# Training loop
epochs = 50
for epoch in range(epochs):
    model.train()
    for inputs, targets in train_loader:
        optimizer.zero_grad() # Reset the gradients
        outputs = model(inputs)
        loss = criterion(outputs, targets)
        loss.backward() # Backpropagation
        optimizer.step() # Update the weights
    
    # Validation step to check performance on test data
    model.eval()
    with torch.no_grad():
        test_loss = 0
        for inputs, targets in test_loader:
            outputs = model(inputs)
            loss = criterion(outputs, targets)
            test_loss += loss.item()
        
    avg_test_loss = test_loss / len(test_loader)
    print(f"Epoch [{epoch+1}/{epochs}], Training Loss: {loss.item():.4f}, Test Loss: {avg_test_loss:.4f}")

print("\nTraining complete!")

# Set the model to evaluation mode
model.eval()

# Make predictions on the test data
with torch.no_grad():
    y_pred_tensor = model(X_test_tensor)

# Convert the predictions back to NumPy arrays
y_pred = y_pred_tensor.cpu().numpy()

# Inverse transform the scaled predictions and actual values to their original price scale
y_test_original = target_scaler.inverse_transform(y_test.reshape(-1, 1))
y_pred_original = target_scaler.inverse_transform(y_pred)

print("Predictions have been made and converted back to original price scale.")

from sklearn.metrics import mean_squared_error, mean_absolute_error

# Calculate evaluation metrics
mse = mean_squared_error(y_test_original, y_pred_original)
mae = mean_absolute_error(y_test_original, y_pred_original)

print(f"Mean Squared Error (MSE): {mse:.2f}")
print(f"Mean Absolute Error (MAE): {mae:.2f}")

import matplotlib.pyplot as plt

# Create a DataFrame for easy plotting
results_df = pd.DataFrame(
    {'Actual': y_test_original.flatten(), 'Predicted': y_pred_original.flatten()},
    index=df.index[len(df) - len(y_test_original):]
)

# Plot the results
plt.figure(figsize=(15, 7))
plt.plot(results_df['Actual'], label='Actual Price', color='blue')
plt.plot(results_df['Predicted'], label='Predicted Price', color='red')
plt.title('Bitcoin Price Prediction (Test Set)')
plt.xlabel('Date')
plt.ylabel('Price (USD)')
plt.legend()
plt.grid(True)
plt.show()

# Plot a zoomed-in version of the last 200 data points to see the details
plt.figure(figsize=(15, 7))
plt.plot(results_df['Actual'][-200:], label='Actual Price', color='blue')
plt.plot(results_df['Predicted'][-200:], label='Predicted Price', color='red')
plt.title('Bitcoin Price Prediction (Last 200 Points)')
plt.xlabel('Date')
plt.ylabel('Price (USD)')
plt.legend()
plt.grid(True)
plt.show()

from pathlib import Path
import json, joblib, torch

artifacts = Path("models")
artifacts.mkdir(exist_ok=True, parents=True)

# Save weights
torch.save(model.state_dict(), artifacts / "lstm_btcusdt_v1.pt")

# Save scalers
joblib.dump(features_scaler, artifacts / "features_scaler.pkl")
joblib.dump(target_scaler, artifacts / "target_scaler.pkl")

# Save config needed for inference (must match training)
config = {
    "symbol": "BTCUSDT",
    "interval": "4h",
    "sequence_length": int(sequence_length),       # 60
    "input_size": int(X_train.shape[2]),           # 9
    "feature_columns": list(features.columns),     # order matters
    "hidden_size": 100,
    "num_layers": 2,
    "output_size": 1
}
(artifacts / "config.json").write_text(json.dumps(config, indent=2))
print("Saved artifacts in models/")

