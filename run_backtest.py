"""Main script to run backtests with the LSTM trading strategy."""

from __future__ import annotations

import argparse
import json
from pathlib import Path
from datetime import datetime
from typing import Optional

from backtest_config import BacktestConfig, create_default_config, load_config, save_config
from backtest_engine import BacktestEngine
from performance_analytics import PerformanceAnalyzer
from visualization import BacktestVisualizer


def run_single_backtest(config: BacktestConfig, output_dir: Optional[str] = None) -> None:
    """Run a single backtest with the given configuration."""
    
    # Set up output directory
    if output_dir is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = f"backtest_results/run_{timestamp}"
    
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    print(f"Running backtest with output directory: {output_dir}")
    print(f"Configuration:")
    print(f"  - Data file: {config.data_file}")
    print(f"  - Date range: {config.start_date} to {config.end_date}")
    print(f"  - Initial capital: ${config.initial_capital:,.2f}")
    print(f"  - Long threshold: {config.trading.long_threshold:.3f}")
    print(f"  - Short threshold: {config.trading.short_threshold:.3f}")
    print(f"  - Position size: {config.trading.position_size_pct:.1%}")
    print(f"  - Commission: {config.trading.commission_pct:.3%}")
    
    # Save configuration
    save_config(config, str(output_path / "config.json"))
    
    # Run backtest
    engine = BacktestEngine(config)
    results = engine.run_backtest()
    
    # Analyze performance
    analyzer = PerformanceAnalyzer(results)
    analyzer.print_summary()
    
    # Save detailed results
    if config.save_trades and results.trades:
        trades_file = output_path / "trades.json"
        with open(trades_file, 'w') as f:
            json.dump(results.trades, f, indent=2)
        print(f"Trades saved to: {trades_file}")
    
    # Save performance report
    analyzer.save_report(str(output_path / "performance_report.json"))
    
    # Save results summary
    results_summary = results.to_dict()
    with open(output_path / "results_summary.json", 'w') as f:
        json.dump(results_summary, f, indent=2)
    
    # Create visualizations
    print("Creating visualizations...")
    visualizer = BacktestVisualizer(results)
    visualizer.save_all_plots(str(output_path / "plots"))
    
    print(f"\nBacktest completed! Results saved to: {output_dir}")
    return results


def run_parameter_sweep() -> None:
    """Run backtests with different parameter combinations."""
    
    print("Running parameter sweep...")
    
    # Base configuration
    base_config = BacktestConfig()
    
    # Parameter combinations to test
    parameter_sets = [
        # Conservative strategy
        {
            "name": "conservative",
            "long_threshold": 0.01,    # 1%
            "short_threshold": -0.01,  # -1%
            "position_size_pct": 0.5,  # 50%
            "commission_pct": 0.001,   # 0.1%
        },
        # Moderate strategy
        {
            "name": "moderate", 
            "long_threshold": 0.005,   # 0.5%
            "short_threshold": -0.005, # -0.5%
            "position_size_pct": 0.75, # 75%
            "commission_pct": 0.001,   # 0.1%
        },
        # Aggressive strategy
        {
            "name": "aggressive",
            "long_threshold": 0.002,   # 0.2%
            "short_threshold": -0.002, # -0.2%
            "position_size_pct": 0.95, # 95%
            "commission_pct": 0.001,   # 0.1%
        },
        # High commission test
        {
            "name": "high_commission",
            "long_threshold": 0.005,   # 0.5%
            "short_threshold": -0.005, # -0.5%
            "position_size_pct": 0.75, # 75%
            "commission_pct": 0.005,   # 0.5%
        }
    ]
    
    results_comparison = []
    
    for params in parameter_sets:
        print(f"\n{'='*60}")
        print(f"Running {params['name'].upper()} strategy")
        print(f"{'='*60}")
        
        # Create configuration
        config = BacktestConfig()
        config.trading.long_threshold = params["long_threshold"]
        config.trading.short_threshold = params["short_threshold"]
        config.trading.position_size_pct = params["position_size_pct"]
        config.trading.commission_pct = params["commission_pct"]
        
        # Run backtest
        output_dir = f"backtest_results/parameter_sweep/{params['name']}"
        results = run_single_backtest(config, output_dir)
        
        # Store results for comparison
        results_comparison.append({
            "strategy": params["name"],
            "parameters": params,
            "total_return": results.total_return,
            "annual_return": results.annual_return,
            "sharpe_ratio": results.sharpe_ratio,
            "max_drawdown": results.max_drawdown,
            "win_rate": results.win_rate,
            "total_trades": results.total_trades,
            "profit_factor": results.profit_factor,
        })
    
    # Save comparison results
    comparison_file = Path("backtest_results/parameter_sweep/comparison.json")
    comparison_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(comparison_file, 'w') as f:
        json.dump(results_comparison, f, indent=2)
    
    # Print comparison summary
    print(f"\n{'='*80}")
    print("PARAMETER SWEEP COMPARISON")
    print(f"{'='*80}")
    print(f"{'Strategy':<15} {'Total Ret':<10} {'Annual Ret':<11} {'Sharpe':<8} {'Max DD':<8} {'Trades':<8} {'Win Rate':<9}")
    print(f"{'-'*80}")
    
    for result in results_comparison:
        print(f"{result['strategy']:<15} "
              f"{result['total_return']*100:>8.1f}% "
              f"{result['annual_return']*100:>9.1f}% "
              f"{result['sharpe_ratio']:>7.2f} "
              f"{result['max_drawdown']*100:>6.1f}% "
              f"{result['total_trades']:>7d} "
              f"{result['win_rate']*100:>7.1f}%")
    
    print(f"\nComparison results saved to: {comparison_file}")


def main():
    """Main function with command line interface."""
    parser = argparse.ArgumentParser(description="Run LSTM trading strategy backtest")
    
    parser.add_argument("--config", type=str, help="Path to configuration file")
    parser.add_argument("--create-config", action="store_true", 
                       help="Create default configuration file")
    parser.add_argument("--parameter-sweep", action="store_true",
                       help="Run parameter sweep with multiple configurations")
    parser.add_argument("--output-dir", type=str, 
                       help="Output directory for results")
    parser.add_argument("--start-date", type=str,
                       help="Start date for backtest (YYYY-MM-DD)")
    parser.add_argument("--end-date", type=str,
                       help="End date for backtest (YYYY-MM-DD)")
    parser.add_argument("--initial-capital", type=float, default=10000,
                       help="Initial capital for backtest")
    parser.add_argument("--long-threshold", type=float, default=0.005,
                       help="Long signal threshold")
    parser.add_argument("--short-threshold", type=float, default=-0.005,
                       help="Short signal threshold")
    parser.add_argument("--position-size", type=float, default=0.95,
                       help="Position size as fraction of capital")
    
    args = parser.parse_args()
    
    # Create default config if requested
    if args.create_config:
        config_path = "backtest_config.json"
        create_default_config(config_path)
        print(f"Default configuration created at: {config_path}")
        print("Edit the configuration file and run again with --config flag")
        return
    
    # Run parameter sweep if requested
    if args.parameter_sweep:
        run_parameter_sweep()
        return
    
    # Load or create configuration
    if args.config:
        config = load_config(args.config)
        print(f"Loaded configuration from: {args.config}")
    else:
        config = BacktestConfig()
        print("Using default configuration")
    
    # Override config with command line arguments
    if args.start_date:
        config.start_date = args.start_date
    if args.end_date:
        config.end_date = args.end_date
    if args.initial_capital:
        config.initial_capital = args.initial_capital
    if args.long_threshold:
        config.trading.long_threshold = args.long_threshold
    if args.short_threshold:
        config.trading.short_threshold = args.short_threshold
    if args.position_size:
        config.trading.position_size_pct = args.position_size
    
    # Run single backtest
    run_single_backtest(config, args.output_dir)


if __name__ == "__main__":
    main()
