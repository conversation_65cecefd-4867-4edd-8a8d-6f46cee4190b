"""Performance analytics and reporting for backtesting results."""

from __future__ import annotations

import json
from pathlib import Path
from typing import Dict, Any, List, Optional

import numpy as np
import pandas as pd

from backtest_config import BacktestResults


class PerformanceAnalyzer:
    """Comprehensive performance analysis for backtest results."""
    
    def __init__(self, results: BacktestResults):
        self.results = results
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report."""
        return {
            "summary": self._get_summary_metrics(),
            "returns_analysis": self._analyze_returns(),
            "trade_analysis": self._analyze_trades(),
            "risk_metrics": self._calculate_risk_metrics(),
            "monthly_returns": self._calculate_monthly_returns(),
            "drawdown_analysis": self._analyze_drawdowns(),
        }
    
    def _get_summary_metrics(self) -> Dict[str, Any]:
        """Get key summary metrics."""
        return {
            "total_return_pct": round(self.results.total_return * 100, 2),
            "annual_return_pct": round(self.results.annual_return * 100, 2),
            "sharpe_ratio": round(self.results.sharpe_ratio, 3),
            "max_drawdown_pct": round(self.results.max_drawdown * 100, 2),
            "win_rate_pct": round(self.results.win_rate * 100, 2),
            "profit_factor": round(self.results.profit_factor, 3),
            "total_trades": self.results.total_trades,
            "final_capital": round(self.results.final_capital, 2),
        }
    
    def _analyze_returns(self) -> Dict[str, Any]:
        """Analyze return characteristics."""
        if not self.results.daily_returns:
            return {}
        
        returns = np.array(self.results.daily_returns)
        
        return {
            "mean_daily_return_pct": round(np.mean(returns) * 100, 4),
            "std_daily_return_pct": round(np.std(returns) * 100, 4),
            "skewness": round(float(pd.Series(returns).skew()), 3),
            "kurtosis": round(float(pd.Series(returns).kurtosis()), 3),
            "best_day_pct": round(np.max(returns) * 100, 2),
            "worst_day_pct": round(np.min(returns) * 100, 2),
            "positive_days": int(np.sum(returns > 0)),
            "negative_days": int(np.sum(returns < 0)),
            "flat_days": int(np.sum(returns == 0)),
        }
    
    def _analyze_trades(self) -> Dict[str, Any]:
        """Analyze individual trades."""
        if not self.results.trades:
            return {}
        
        trades_df = pd.DataFrame(self.results.trades)
        
        # Convert percentage returns to actual percentages
        pnl_pcts = trades_df['pnl_pct'] * 100
        
        # Winning and losing trades
        winning_trades = trades_df[trades_df['pnl'] > 0]
        losing_trades = trades_df[trades_df['pnl'] < 0]
        
        # Consecutive wins/losses
        win_streak, loss_streak = self._calculate_streaks(trades_df['pnl'])
        
        analysis = {
            "avg_trade_return_pct": round(pnl_pcts.mean(), 3),
            "median_trade_return_pct": round(pnl_pcts.median(), 3),
            "std_trade_return_pct": round(pnl_pcts.std(), 3),
            "best_trade_pct": round(pnl_pcts.max(), 2),
            "worst_trade_pct": round(pnl_pcts.min(), 2),
            "avg_holding_periods": round(trades_df['holding_periods'].mean(), 1),
            "max_holding_periods": int(trades_df['holding_periods'].max()),
            "min_holding_periods": int(trades_df['holding_periods'].min()),
            "max_consecutive_wins": win_streak,
            "max_consecutive_losses": loss_streak,
        }
        
        # Add position type analysis
        if 'position_type' in trades_df.columns:
            long_trades = trades_df[trades_df['position_type'] == 'LONG']
            short_trades = trades_df[trades_df['position_type'] == 'SHORT']
            
            analysis.update({
                "long_trades": len(long_trades),
                "short_trades": len(short_trades),
                "long_win_rate_pct": round((long_trades['pnl'] > 0).mean() * 100, 2) if len(long_trades) > 0 else 0,
                "short_win_rate_pct": round((short_trades['pnl'] > 0).mean() * 100, 2) if len(short_trades) > 0 else 0,
                "avg_long_return_pct": round((long_trades['pnl_pct'] * 100).mean(), 3) if len(long_trades) > 0 else 0,
                "avg_short_return_pct": round((short_trades['pnl_pct'] * 100).mean(), 3) if len(short_trades) > 0 else 0,
            })
        
        return analysis
    
    def _calculate_risk_metrics(self) -> Dict[str, Any]:
        """Calculate additional risk metrics."""
        if not self.results.daily_returns:
            return {}
        
        returns = np.array(self.results.daily_returns)
        
        # Value at Risk (VaR) at 95% confidence
        var_95 = np.percentile(returns, 5)
        
        # Conditional Value at Risk (CVaR)
        cvar_95 = np.mean(returns[returns <= var_95])
        
        # Calmar ratio (annual return / max drawdown)
        calmar_ratio = (self.results.annual_return / self.results.max_drawdown 
                       if self.results.max_drawdown > 0 else 0)
        
        # Sortino ratio (using downside deviation)
        downside_returns = returns[returns < 0]
        downside_std = np.std(downside_returns) if len(downside_returns) > 0 else 0
        sortino_ratio = (np.mean(returns) / downside_std * np.sqrt(252) 
                        if downside_std > 0 else 0)
        
        return {
            "var_95_pct": round(var_95 * 100, 3),
            "cvar_95_pct": round(cvar_95 * 100, 3),
            "calmar_ratio": round(calmar_ratio, 3),
            "sortino_ratio": round(sortino_ratio, 3),
            "downside_deviation_pct": round(downside_std * 100, 4),
        }
    
    def _calculate_monthly_returns(self) -> List[Dict[str, Any]]:
        """Calculate monthly return breakdown."""
        if not self.results.equity_curve or len(self.results.equity_curve) < 2:
            return []
        
        # Create equity series with timestamps
        # Note: This is simplified - in practice you'd need actual timestamps
        equity_series = pd.Series(self.results.equity_curve)
        returns = equity_series.pct_change().dropna()
        
        # Group by month (simplified - assumes regular intervals)
        monthly_data = []
        chunk_size = max(1, len(returns) // 12)  # Approximate monthly chunks
        
        for i in range(0, len(returns), chunk_size):
            chunk = returns.iloc[i:i+chunk_size]
            if len(chunk) > 0:
                monthly_return = (1 + chunk).prod() - 1
                monthly_data.append({
                    "period": f"Month_{len(monthly_data)+1}",
                    "return_pct": round(monthly_return * 100, 2),
                    "trades": len(chunk),  # Approximate
                })
        
        return monthly_data
    
    def _analyze_drawdowns(self) -> Dict[str, Any]:
        """Analyze drawdown characteristics."""
        if not self.results.equity_curve:
            return {}
        
        equity = np.array(self.results.equity_curve)
        peak = np.maximum.accumulate(equity)
        drawdown = (equity - peak) / peak
        
        # Find drawdown periods
        in_drawdown = drawdown < 0
        drawdown_periods = []
        
        if np.any(in_drawdown):
            # Find start and end of drawdown periods
            start_indices = np.where(np.diff(in_drawdown.astype(int)) == 1)[0] + 1
            end_indices = np.where(np.diff(in_drawdown.astype(int)) == -1)[0] + 1
            
            # Handle edge cases
            if in_drawdown[0]:
                start_indices = np.concatenate([[0], start_indices])
            if in_drawdown[-1]:
                end_indices = np.concatenate([end_indices, [len(in_drawdown)]])
            
            for start, end in zip(start_indices, end_indices):
                period_drawdown = drawdown[start:end]
                max_dd_in_period = abs(np.min(period_drawdown))
                duration = end - start
                
                drawdown_periods.append({
                    "max_drawdown_pct": round(max_dd_in_period * 100, 2),
                    "duration_periods": duration,
                })
        
        # Sort by severity
        drawdown_periods.sort(key=lambda x: x["max_drawdown_pct"], reverse=True)
        
        return {
            "max_drawdown_pct": round(abs(np.min(drawdown)) * 100, 2),
            "avg_drawdown_pct": round(np.mean(abs(drawdown[drawdown < 0])) * 100, 2) if np.any(drawdown < 0) else 0,
            "drawdown_periods": len(drawdown_periods),
            "longest_drawdown_periods": max([dd["duration_periods"] for dd in drawdown_periods], default=0),
            "top_5_drawdowns": drawdown_periods[:5],
        }
    
    def _calculate_streaks(self, pnl_series: pd.Series) -> tuple[int, int]:
        """Calculate maximum consecutive wins and losses."""
        if len(pnl_series) == 0:
            return 0, 0
        
        wins = (pnl_series > 0).astype(int)
        losses = (pnl_series < 0).astype(int)
        
        max_win_streak = 0
        max_loss_streak = 0
        current_win_streak = 0
        current_loss_streak = 0
        
        for win, loss in zip(wins, losses):
            if win:
                current_win_streak += 1
                current_loss_streak = 0
                max_win_streak = max(max_win_streak, current_win_streak)
            elif loss:
                current_loss_streak += 1
                current_win_streak = 0
                max_loss_streak = max(max_loss_streak, current_loss_streak)
            else:
                current_win_streak = 0
                current_loss_streak = 0
        
        return max_win_streak, max_loss_streak
    
    def save_report(self, output_path: str) -> None:
        """Save performance report to JSON file."""
        report = self.generate_report()
        
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"Performance report saved to: {output_path}")
    
    def print_summary(self) -> None:
        """Print a formatted summary of key metrics."""
        summary = self._get_summary_metrics()
        
        print("\n" + "="*60)
        print("BACKTEST PERFORMANCE SUMMARY")
        print("="*60)
        print(f"Total Return:        {summary['total_return_pct']:>8.2f}%")
        print(f"Annual Return:       {summary['annual_return_pct']:>8.2f}%")
        print(f"Sharpe Ratio:        {summary['sharpe_ratio']:>8.3f}")
        print(f"Max Drawdown:        {summary['max_drawdown_pct']:>8.2f}%")
        print(f"Win Rate:            {summary['win_rate_pct']:>8.2f}%")
        print(f"Profit Factor:       {summary['profit_factor']:>8.3f}")
        print(f"Total Trades:        {summary['total_trades']:>8d}")
        print(f"Final Capital:       ${summary['final_capital']:>8,.2f}")
        print("="*60)
        
        # Print trade analysis if available
        trade_analysis = self._analyze_trades()
        if trade_analysis:
            print("\nTRADE ANALYSIS")
            print("-"*30)
            print(f"Avg Trade Return:    {trade_analysis.get('avg_trade_return_pct', 0):>8.3f}%")
            print(f"Best Trade:          {trade_analysis.get('best_trade_pct', 0):>8.2f}%")
            print(f"Worst Trade:         {trade_analysis.get('worst_trade_pct', 0):>8.2f}%")
            print(f"Avg Holding:         {trade_analysis.get('avg_holding_periods', 0):>8.1f} periods")
            print(f"Max Win Streak:      {trade_analysis.get('max_consecutive_wins', 0):>8d}")
            print(f"Max Loss Streak:     {trade_analysis.get('max_consecutive_losses', 0):>8d}")
        
        print("\n")
