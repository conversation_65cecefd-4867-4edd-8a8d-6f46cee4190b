"""Simple example of how to run a backtest with the LSTM trading strategy."""

from backtest_config import BacktestConfig
from backtest_engine import BacktestEngine
from performance_analytics import PerformanceAnalyzer
from visualization import BacktestVisualizer


def main():
    """Run a simple backtest example."""
    
    print("LSTM Trading Strategy Backtest Example")
    print("="*50)
    
    # Create configuration
    config = BacktestConfig()
    
    # Customize some parameters
    config.initial_capital = 10000.0
    config.trading.long_threshold = 0.005   # 0.5% predicted increase
    config.trading.short_threshold = -0.005 # 0.5% predicted decrease
    config.trading.position_size_pct = 0.8  # Use 80% of capital per trade
    config.trading.commission_pct = 0.001   # 0.1% commission
    config.trading.slippage_pct = 0.0005    # 0.05% slippage
    
    # Optional: Set date range for backtest
    # config.start_date = "2023-01-01"
    # config.end_date = "2024-01-01"
    
    print(f"Configuration:")
    print(f"  Initial Capital: ${config.initial_capital:,.2f}")
    print(f"  Long Threshold: {config.trading.long_threshold:.3f}")
    print(f"  Short Threshold: {config.trading.short_threshold:.3f}")
    print(f"  Position Size: {config.trading.position_size_pct:.1%}")
    print(f"  Commission: {config.trading.commission_pct:.3%}")
    print(f"  Slippage: {config.trading.slippage_pct:.4%}")
    print()
    
    # Run backtest
    print("Running backtest...")
    engine = BacktestEngine(config)
    results = engine.run_backtest()
    
    # Analyze results
    analyzer = PerformanceAnalyzer(results)
    analyzer.print_summary()
    
    # Create visualizations
    print("Creating visualizations...")
    visualizer = BacktestVisualizer(results)
    
    # Show equity curve
    fig1 = visualizer.plot_equity_curve(show_drawdown=True)
    fig1.show()
    
    # Show trade analysis if we have trades
    if results.trades:
        fig2 = visualizer.plot_trade_analysis()
        fig2.show()
    
    # Show returns analysis
    if results.daily_returns:
        fig3 = visualizer.plot_returns_analysis()
        fig3.show()
    
    # Create comprehensive dashboard
    fig4 = visualizer.create_summary_dashboard()
    fig4.show()
    
    print("\nBacktest completed!")
    print("Close the plot windows to exit.")
    
    # Keep plots open
    import matplotlib.pyplot as plt
    plt.show()


if __name__ == "__main__":
    main()
