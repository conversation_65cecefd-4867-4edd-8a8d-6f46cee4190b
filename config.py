"""Configuration loader for Binance API credentials.

Loads `BINANCE_API_KEY` and `BINANCE_API_SECRET` only from a local `.env` file.
No environment-variable or alternate-file fallbacks are used.
"""

from __future__ import annotations

from typing import <PERSON>ple
from pathlib import Path

from dotenv import dotenv_values


def load_binance_credentials() -> Tuple[str, str]:
    """Load Binance API credentials strictly from the `.env` file.

    Returns:
        (api_key, api_secret)

    Raises:
        ValueError: If `.env` does not exist or either key is missing.
    """

    env_path = Path(".env")
    if not env_path.exists():
        raise ValueError(
            "Missing .env file. Create a .env file with BINANCE_API_KEY and BINANCE_API_SECRET."
        )

    values = dotenv_values(dotenv_path=env_path)
    api_key = (values.get("BINANCE_API_KEY") or "").strip()
    api_secret = (values.get("BINANCE_API_SECRET") or "").strip()

    if not api_key or not api_secret:
        raise ValueError(
            "Missing BINANCE_API_KEY or BINANCE_API_SECRET in .env. Add both values to the .env file."
        )

    return api_key, api_secret


