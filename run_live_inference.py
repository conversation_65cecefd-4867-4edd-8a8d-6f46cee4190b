import time
import json
from pathlib import Path
from typing import List
import requests
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import joblib

from fetch_btcusdt_4h import klines_to_dataframe, add_indicators

BINANCE_API_BASE_URL = "https://api.binance.com"

class LSTM_Model(nn.Module):
    def __init__(self, input_size, hidden_size, num_layers, output_size):
        super().__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True)
        self.fc = nn.Linear(hidden_size, output_size)

    def forward(self, x):
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size, device=x.device)
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size, device=x.device)
        out, _ = self.lstm(x, (h0, c0))
        out = self.fc(out[:, -1, :])
        return out

def fetch_recent_klines(symbol: str, interval: str, limit: int = 1000) -> List[List]:
    resp = requests.get(
        f"{BINANCE_API_BASE_URL}/api/v3/klines",
        params={"symbol": symbol.upper(), "interval": interval, "limit": limit},
        timeout=30,
    )
    resp.raise_for_status()
    return resp.json()

def load_assets():
    artifacts = Path("models")
    cfg = json.loads((artifacts / "config.json").read_text())
    features_scaler = joblib.load(artifacts / "features_scaler.pkl")
    target_scaler = joblib.load(artifacts / "target_scaler.pkl")

    model = LSTM_Model(
        input_size=cfg["input_size"],
        hidden_size=cfg["hidden_size"],
        num_layers=cfg["num_layers"],
        output_size=cfg["output_size"],
    )
    state = torch.load(artifacts / "lstm_btcusdt_v1.pt", map_location="cpu")
    model.load_state_dict(state)
    model.eval()

    return cfg, features_scaler, target_scaler, model

def build_latest_sequence(df: pd.DataFrame, cfg, features_scaler, target_scaler) -> (np.ndarray, float):
    # Mirror training prep
    df = df.copy()
    df = df.iloc[1:]              # drop first RSI NaN row as in training
    df = df.iloc[:-1]             # drop possibly-open current candle

    feature_cols = cfg["feature_columns"]
    features = df[feature_cols]
    target = df[["close"]]

    scaled_features = features_scaler.transform(features)
    scaled_close = target_scaler.transform(target)

    scaled = pd.DataFrame(scaled_features, columns=feature_cols, index=df.index)
    scaled["close"] = scaled_close  # ensure 'close' is last column

    seq_len = cfg["sequence_length"]
    if len(scaled) < seq_len:
        raise ValueError(f"Not enough rows to form a sequence of length {seq_len} (have {len(scaled)})")

    X = scaled.iloc[-seq_len:, :].values.astype(np.float32)  # (seq_len, 9)
    X = np.expand_dims(X, axis=0)  # (1, seq_len, 9)

    last_close = float(target.iloc[-1, 0])
    return X, last_close

def decide_signal(pred_price: float, last_close: float, up=0.005, down=-0.005):
    delta = (pred_price - last_close) / last_close
    if delta >= up:
        return "LONG", delta
    elif delta <= down:
        return "SHORT", delta
    else:
        return "FLAT", delta

def main():
    cfg, features_scaler, target_scaler, model = load_assets()

    rows = fetch_recent_klines(cfg["symbol"], cfg["interval"], limit=1000)
    df = klines_to_dataframe(rows)
    df = add_indicators(df)

    X_np, last_close = build_latest_sequence(df, cfg, features_scaler, target_scaler)

    with torch.no_grad():
        X_tensor = torch.from_numpy(X_np)  # cpu
        pred_scaled = model(X_tensor).numpy()  # shape (1,1)

    pred_price = float(target_scaler.inverse_transform(pred_scaled)[0, 0])
    signal, delta = decide_signal(pred_price, last_close)

    ts = df["close_time"].iloc[-2]  # last fully closed candle close time
    print(json.dumps({
        "timestamp_close_utc": str(ts),
        "symbol": cfg["symbol"],
        "interval": cfg["interval"],
        "last_close": round(last_close, 2),
        "pred_next_close": round(pred_price, 2),
        "delta_pct": round(delta * 100, 3),
        "signal": signal
    }, indent=2))

if __name__ == "__main__":
    main()