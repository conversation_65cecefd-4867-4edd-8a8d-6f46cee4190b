"""Visualization tools for backtesting results."""

from __future__ import annotations

from pathlib import Path
from typing import List, Optional

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.figure import Figure

from backtest_config import BacktestResults
from performance_analytics import PerformanceAnalyzer


class BacktestVisualizer:
    """Create visualizations for backtest results."""
    
    def __init__(self, results: BacktestResults, timestamps: Optional[List] = None):
        self.results = results
        self.timestamps = timestamps or list(range(len(results.equity_curve)))
        self.analyzer = PerformanceAnalyzer(results)
    
    def plot_equity_curve(self, save_path: Optional[str] = None, 
                         show_drawdown: bool = True) -> Figure:
        """Plot equity curve with optional drawdown."""
        fig, axes = plt.subplots(2 if show_drawdown else 1, 1, 
                                figsize=(15, 10 if show_drawdown else 6))
        
        if not show_drawdown:
            axes = [axes]
        
        # Plot equity curve
        equity = np.array(self.results.equity_curve)
        axes[0].plot(self.timestamps, equity, linewidth=2, color='blue', label='Portfolio Value')
        axes[0].axhline(y=self.results.equity_curve[0], color='gray', 
                       linestyle='--', alpha=0.7, label='Initial Capital')
        
        axes[0].set_title('Portfolio Equity Curve', fontsize=16, fontweight='bold')
        axes[0].set_ylabel('Portfolio Value ($)', fontsize=12)
        axes[0].grid(True, alpha=0.3)
        axes[0].legend()
        
        # Format y-axis
        axes[0].yaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'${x:,.0f}'))
        
        # Add performance metrics as text
        total_return = self.results.total_return * 100
        max_dd = self.results.max_drawdown * 100
        sharpe = self.results.sharpe_ratio
        
        textstr = f'Total Return: {total_return:.1f}%\nMax Drawdown: {max_dd:.1f}%\nSharpe Ratio: {sharpe:.2f}'
        props = dict(boxstyle='round', facecolor='wheat', alpha=0.8)
        axes[0].text(0.02, 0.98, textstr, transform=axes[0].transAxes, fontsize=10,
                    verticalalignment='top', bbox=props)
        
        # Plot drawdown if requested
        if show_drawdown:
            peak = np.maximum.accumulate(equity)
            drawdown = (equity - peak) / peak * 100
            
            axes[1].fill_between(self.timestamps, drawdown, 0, alpha=0.3, color='red')
            axes[1].plot(self.timestamps, drawdown, color='red', linewidth=1)
            axes[1].set_title('Drawdown', fontsize=14, fontweight='bold')
            axes[1].set_ylabel('Drawdown (%)', fontsize=12)
            axes[1].set_xlabel('Time', fontsize=12)
            axes[1].grid(True, alpha=0.3)
            axes[1].axhline(y=0, color='black', linewidth=0.5)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Equity curve saved to: {save_path}")
        
        return fig
    
    def plot_trade_analysis(self, save_path: Optional[str] = None) -> Figure:
        """Plot trade analysis charts."""
        if not self.results.trades:
            print("No trades to analyze")
            return None
        
        trades_df = pd.DataFrame(self.results.trades)
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Trade Analysis', fontsize=16, fontweight='bold')
        
        # 1. Trade P&L distribution
        pnl_pct = trades_df['pnl_pct'] * 100
        axes[0, 0].hist(pnl_pct, bins=20, alpha=0.7, color='blue', edgecolor='black')
        axes[0, 0].axvline(x=0, color='red', linestyle='--', alpha=0.7)
        axes[0, 0].set_title('Trade Return Distribution')
        axes[0, 0].set_xlabel('Return (%)')
        axes[0, 0].set_ylabel('Frequency')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. Cumulative P&L
        cumulative_pnl = np.cumsum(trades_df['pnl'])
        trade_numbers = range(1, len(cumulative_pnl) + 1)
        axes[0, 1].plot(trade_numbers, cumulative_pnl, linewidth=2, color='green')
        axes[0, 1].axhline(y=0, color='red', linestyle='--', alpha=0.7)
        axes[0, 1].set_title('Cumulative P&L')
        axes[0, 1].set_xlabel('Trade Number')
        axes[0, 1].set_ylabel('Cumulative P&L ($)')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. Holding periods
        axes[1, 0].hist(trades_df['holding_periods'], bins=15, alpha=0.7, 
                       color='orange', edgecolor='black')
        axes[1, 0].set_title('Holding Periods Distribution')
        axes[1, 0].set_xlabel('Holding Periods')
        axes[1, 0].set_ylabel('Frequency')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. Win/Loss by position type (if available)
        if 'position_type' in trades_df.columns:
            position_types = trades_df['position_type'].unique()
            win_rates = []
            
            for pos_type in position_types:
                subset = trades_df[trades_df['position_type'] == pos_type]
                win_rate = (subset['pnl'] > 0).mean() * 100
                win_rates.append(win_rate)
            
            bars = axes[1, 1].bar(position_types, win_rates, alpha=0.7, 
                                 color=['green', 'red'][:len(position_types)])
            axes[1, 1].set_title('Win Rate by Position Type')
            axes[1, 1].set_ylabel('Win Rate (%)')
            axes[1, 1].set_ylim(0, 100)
            axes[1, 1].grid(True, alpha=0.3)
            
            # Add value labels on bars
            for bar, rate in zip(bars, win_rates):
                height = bar.get_height()
                axes[1, 1].text(bar.get_x() + bar.get_width()/2., height + 1,
                               f'{rate:.1f}%', ha='center', va='bottom')
        else:
            # Alternative: Monthly returns if no position type data
            axes[1, 1].text(0.5, 0.5, 'Position type\ndata not available', 
                           ha='center', va='center', transform=axes[1, 1].transAxes,
                           fontsize=12)
            axes[1, 1].set_title('Position Type Analysis')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Trade analysis saved to: {save_path}")
        
        return fig
    
    def plot_returns_analysis(self, save_path: Optional[str] = None) -> Figure:
        """Plot returns analysis."""
        if not self.results.daily_returns:
            print("No daily returns data available")
            return None
        
        returns = np.array(self.results.daily_returns) * 100  # Convert to percentage
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Returns Analysis', fontsize=16, fontweight='bold')
        
        # 1. Returns distribution
        axes[0, 0].hist(returns, bins=30, alpha=0.7, color='blue', 
                       edgecolor='black', density=True)
        axes[0, 0].axvline(x=0, color='red', linestyle='--', alpha=0.7)
        axes[0, 0].set_title('Daily Returns Distribution')
        axes[0, 0].set_xlabel('Daily Return (%)')
        axes[0, 0].set_ylabel('Density')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. Rolling Sharpe ratio (if enough data)
        if len(returns) > 30:
            window = min(30, len(returns) // 4)
            rolling_sharpe = pd.Series(returns).rolling(window).apply(
                lambda x: x.mean() / x.std() * np.sqrt(252) if x.std() > 0 else 0
            )
            axes[0, 1].plot(rolling_sharpe, linewidth=2, color='green')
            axes[0, 1].axhline(y=0, color='red', linestyle='--', alpha=0.7)
            axes[0, 1].set_title(f'Rolling Sharpe Ratio ({window}-period)')
            axes[0, 1].set_xlabel('Period')
            axes[0, 1].set_ylabel('Sharpe Ratio')
            axes[0, 1].grid(True, alpha=0.3)
        else:
            axes[0, 1].text(0.5, 0.5, 'Insufficient data\nfor rolling Sharpe', 
                           ha='center', va='center', transform=axes[0, 1].transAxes)
        
        # 3. Q-Q plot for normality check
        from scipy import stats
        stats.probplot(returns, dist="norm", plot=axes[1, 0])
        axes[1, 0].set_title('Q-Q Plot (Normality Check)')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. Autocorrelation
        if len(returns) > 10:
            max_lags = min(20, len(returns) // 2)
            autocorr = [np.corrcoef(returns[:-i], returns[i:])[0, 1] 
                       for i in range(1, max_lags + 1)]
            
            axes[1, 1].bar(range(1, len(autocorr) + 1), autocorr, alpha=0.7, color='purple')
            axes[1, 1].axhline(y=0, color='black', linewidth=0.5)
            axes[1, 1].set_title('Returns Autocorrelation')
            axes[1, 1].set_xlabel('Lag')
            axes[1, 1].set_ylabel('Correlation')
            axes[1, 1].grid(True, alpha=0.3)
        else:
            axes[1, 1].text(0.5, 0.5, 'Insufficient data\nfor autocorrelation', 
                           ha='center', va='center', transform=axes[1, 1].transAxes)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Returns analysis saved to: {save_path}")
        
        return fig
    
    def create_summary_dashboard(self, save_path: Optional[str] = None) -> Figure:
        """Create a comprehensive dashboard with key metrics."""
        fig = plt.figure(figsize=(20, 12))
        
        # Create grid layout
        gs = fig.add_gridspec(3, 4, hspace=0.3, wspace=0.3)
        
        # 1. Equity curve (spans 2 columns)
        ax1 = fig.add_subplot(gs[0, :2])
        equity = np.array(self.results.equity_curve)
        ax1.plot(self.timestamps, equity, linewidth=2, color='blue')
        ax1.set_title('Portfolio Equity Curve', fontweight='bold')
        ax1.set_ylabel('Value ($)')
        ax1.grid(True, alpha=0.3)
        ax1.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'${x:,.0f}'))
        
        # 2. Drawdown (spans 2 columns)
        ax2 = fig.add_subplot(gs[0, 2:])
        peak = np.maximum.accumulate(equity)
        drawdown = (equity - peak) / peak * 100
        ax2.fill_between(self.timestamps, drawdown, 0, alpha=0.3, color='red')
        ax2.plot(self.timestamps, drawdown, color='red', linewidth=1)
        ax2.set_title('Drawdown', fontweight='bold')
        ax2.set_ylabel('Drawdown (%)')
        ax2.grid(True, alpha=0.3)
        
        # 3. Key metrics table
        ax3 = fig.add_subplot(gs[1, :2])
        ax3.axis('off')
        
        metrics_data = [
            ['Total Return', f"{self.results.total_return*100:.2f}%"],
            ['Annual Return', f"{self.results.annual_return*100:.2f}%"],
            ['Sharpe Ratio', f"{self.results.sharpe_ratio:.3f}"],
            ['Max Drawdown', f"{self.results.max_drawdown*100:.2f}%"],
            ['Win Rate', f"{self.results.win_rate*100:.2f}%"],
            ['Profit Factor', f"{self.results.profit_factor:.3f}"],
            ['Total Trades', f"{self.results.total_trades}"],
            ['Final Capital', f"${self.results.final_capital:,.2f}"],
        ]
        
        table = ax3.table(cellText=metrics_data, 
                         colLabels=['Metric', 'Value'],
                         cellLoc='left',
                         loc='center',
                         colWidths=[0.6, 0.4])
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1, 2)
        ax3.set_title('Performance Metrics', fontweight='bold', pad=20)
        
        # 4. Trade P&L distribution
        if self.results.trades:
            ax4 = fig.add_subplot(gs[1, 2:])
            trades_df = pd.DataFrame(self.results.trades)
            pnl_pct = trades_df['pnl_pct'] * 100
            ax4.hist(pnl_pct, bins=15, alpha=0.7, color='green', edgecolor='black')
            ax4.axvline(x=0, color='red', linestyle='--', alpha=0.7)
            ax4.set_title('Trade Returns Distribution', fontweight='bold')
            ax4.set_xlabel('Return (%)')
            ax4.set_ylabel('Frequency')
            ax4.grid(True, alpha=0.3)
        
        # 5. Monthly returns (simplified)
        ax5 = fig.add_subplot(gs[2, :])
        if len(self.results.equity_curve) > 12:
            # Create simplified monthly returns
            chunk_size = max(1, len(self.results.equity_curve) // 12)
            monthly_returns = []
            
            for i in range(0, len(self.results.equity_curve) - chunk_size, chunk_size):
                start_val = self.results.equity_curve[i]
                end_val = self.results.equity_curve[i + chunk_size]
                monthly_ret = (end_val - start_val) / start_val * 100
                monthly_returns.append(monthly_ret)
            
            months = [f'M{i+1}' for i in range(len(monthly_returns))]
            colors = ['green' if ret > 0 else 'red' for ret in monthly_returns]
            
            ax5.bar(months, monthly_returns, color=colors, alpha=0.7)
            ax5.axhline(y=0, color='black', linewidth=0.5)
            ax5.set_title('Monthly Returns', fontweight='bold')
            ax5.set_ylabel('Return (%)')
            ax5.grid(True, alpha=0.3)
            
            # Rotate x-axis labels if too many
            if len(months) > 10:
                plt.setp(ax5.get_xticklabels(), rotation=45)
        
        plt.suptitle('Backtest Results Dashboard', fontsize=20, fontweight='bold', y=0.98)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Dashboard saved to: {save_path}")
        
        return fig
    
    def save_all_plots(self, output_dir: str) -> None:
        """Save all visualization plots to specified directory."""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Create all plots
        self.plot_equity_curve(str(output_path / "equity_curve.png"))
        self.plot_trade_analysis(str(output_path / "trade_analysis.png"))
        self.plot_returns_analysis(str(output_path / "returns_analysis.png"))
        self.create_summary_dashboard(str(output_path / "dashboard.png"))
        
        print(f"All plots saved to: {output_dir}")
        
        # Close all figures to free memory
        plt.close('all')
