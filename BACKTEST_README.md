# LSTM Trading Strategy Backtesting Framework

This backtesting framework allows you to test your LSTM-based Bitcoin trading strategy on historical data with comprehensive performance analysis and visualization.

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Run Simple Example
```bash
python example_backtest.py
```

### 3. Run Full Backtest with CLI
```bash
python run_backtest.py
```

## 📁 Framework Components

### Core Files
- **`backtest_engine.py`** - Main backtesting engine
- **`portfolio.py`** - Portfolio management and trade execution
- **`backtest_config.py`** - Configuration system
- **`performance_analytics.py`** - Performance metrics calculation
- **`visualization.py`** - Plotting and visualization tools

### Scripts
- **`run_backtest.py`** - Main CLI script with advanced options
- **`example_backtest.py`** - Simple example for getting started

## ⚙️ Configuration

### Create Default Config
```bash
python run_backtest.py --create-config
```

This creates `backtest_config.json` with default parameters:

```json
{
  "data_file": "data/BTCUSDT_4h.csv",
  "initial_capital": 10000.0,
  "trading": {
    "long_threshold": 0.005,
    "short_threshold": -0.005,
    "position_size_pct": 0.95,
    "commission_pct": 0.001,
    "slippage_pct": 0.0005
  }
}
```

### Key Parameters

#### Trading Strategy
- **`long_threshold`**: Minimum predicted price increase to go long (e.g., 0.005 = 0.5%)
- **`short_threshold`**: Maximum predicted price decrease to go short (e.g., -0.005 = -0.5%)
- **`position_size_pct`**: Fraction of capital to use per trade (0.95 = 95%)

#### Risk Management
- **`stop_loss_pct`**: Stop loss percentage (null = disabled)
- **`take_profit_pct`**: Take profit percentage (null = disabled)
- **`max_holding_periods`**: Maximum periods to hold position (null = no limit)

#### Transaction Costs
- **`commission_pct`**: Commission per trade (0.001 = 0.1%)
- **`slippage_pct`**: Slippage per trade (0.0005 = 0.05%)

## 🎯 Usage Examples

### Basic Backtest
```bash
python run_backtest.py --initial-capital 10000 --long-threshold 0.005
```

### Date Range Backtest
```bash
python run_backtest.py --start-date 2023-01-01 --end-date 2024-01-01
```

### Custom Configuration
```bash
python run_backtest.py --config my_config.json --output-dir my_results
```

### Parameter Sweep
```bash
python run_backtest.py --parameter-sweep
```

This runs multiple backtests with different parameter combinations and compares results.

## 📊 Output Files

Each backtest creates a timestamped directory with:

```
backtest_results/run_20241209_143022/
├── config.json              # Configuration used
├── results_summary.json     # Key metrics summary
├── performance_report.json  # Detailed performance analysis
├── trades.json              # Individual trade records
└── plots/                   # Visualization files
    ├── equity_curve.png
    ├── trade_analysis.png
    ├── returns_analysis.png
    └── dashboard.png
```

## 📈 Performance Metrics

### Returns
- **Total Return**: Overall portfolio return
- **Annual Return**: Annualized return
- **Sharpe Ratio**: Risk-adjusted return metric
- **Sortino Ratio**: Downside risk-adjusted return

### Risk Metrics
- **Maximum Drawdown**: Largest peak-to-trough decline
- **Value at Risk (VaR)**: Potential loss at 95% confidence
- **Calmar Ratio**: Annual return / max drawdown

### Trade Analysis
- **Win Rate**: Percentage of profitable trades
- **Profit Factor**: Gross profit / gross loss
- **Average Win/Loss**: Average profit/loss per trade
- **Consecutive Wins/Losses**: Maximum streaks

## 🔧 Advanced Usage

### Custom Strategy Parameters

```python
from backtest_config import BacktestConfig
from backtest_engine import BacktestEngine

# Create custom configuration
config = BacktestConfig()
config.trading.long_threshold = 0.01    # More conservative
config.trading.short_threshold = -0.01
config.trading.stop_loss_pct = 0.05     # 5% stop loss
config.trading.take_profit_pct = 0.10   # 10% take profit

# Run backtest
engine = BacktestEngine(config)
results = engine.run_backtest()
```

### Custom Analysis

```python
from performance_analytics import PerformanceAnalyzer
from visualization import BacktestVisualizer

# Analyze results
analyzer = PerformanceAnalyzer(results)
report = analyzer.generate_report()

# Create custom visualizations
visualizer = BacktestVisualizer(results)
fig = visualizer.plot_equity_curve()
fig.show()
```

## 🎨 Visualization Features

### Equity Curve
- Portfolio value over time
- Drawdown periods
- Performance metrics overlay

### Trade Analysis
- P&L distribution histogram
- Cumulative P&L progression
- Holding period analysis
- Win rate by position type

### Returns Analysis
- Daily returns distribution
- Rolling Sharpe ratio
- Autocorrelation analysis
- Q-Q plot for normality

### Dashboard
- Comprehensive overview
- Key metrics table
- Multiple chart types
- Monthly returns breakdown

## ⚠️ Important Notes

### Data Requirements
- Ensure `data/BTCUSDT_4h.csv` exists with historical data
- Model files must be in `models/` directory:
  - `lstm_btcusdt_v1.pt` (trained model)
  - `features_scaler.pkl` (feature scaler)
  - `target_scaler.pkl` (target scaler)
  - `config.json` (model configuration)

### Model Compatibility
- The backtesting framework uses the same data preprocessing as training
- Sequence length must match training configuration (default: 60)
- Feature columns must match exactly

### Performance Considerations
- Backtests can take several minutes for full historical data
- Use date ranges to test specific periods
- Parameter sweeps run multiple backtests sequentially

## 🐛 Troubleshooting

### Common Issues

1. **"Not enough data for sequence"**
   - Ensure you have at least 100+ rows after the sequence length
   - Check start_date isn't too recent

2. **"Model files not found"**
   - Verify model artifacts exist in `models/` directory
   - Run training notebook first if needed

3. **"Feature mismatch"**
   - Ensure data has all required columns (RSI, MACD, etc.)
   - Check feature column order matches training

### Debug Mode
Add print statements or use Python debugger:
```python
import pdb; pdb.set_trace()  # Add breakpoint
```

## 📝 Next Steps

1. **Optimize Parameters**: Use parameter sweep to find best settings
2. **Add Features**: Extend with additional technical indicators
3. **Risk Management**: Implement more sophisticated risk rules
4. **Walk-Forward Analysis**: Test strategy robustness over time
5. **Live Trading**: Adapt for real-time trading (carefully!)

## 🤝 Contributing

Feel free to extend the framework with:
- Additional performance metrics
- New visualization types
- Alternative risk management rules
- Different position sizing methods
- Multi-asset support
