"""Core backtesting engine for LSTM trading strategy."""

from __future__ import annotations

import json
import warnings
from pathlib import Path
from typing import Tuple, Optional, Dict, Any

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import joblib

from fetch_btcusdt_4h import add_indicators
from portfolio import Portfolio, PositionType
from backtest_config import BacktestConfig, BacktestResults

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')


class LSTM_Model(nn.Module):
    """LSTM model for price prediction."""
    
    def __init__(self, input_size: int, hidden_size: int, num_layers: int, output_size: int):
        super().__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True)
        self.fc = nn.Linear(hidden_size, output_size)

    def forward(self, x):
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size, device=x.device)
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size, device=x.device)
        out, _ = self.lstm(x, (h0, c0))
        out = self.fc(out[:, -1, :])
        return out


class BacktestEngine:
    """Main backtesting engine."""
    
    def __init__(self, config: BacktestConfig):
        self.config = config
        self.model = None
        self.features_scaler = None
        self.target_scaler = None
        self.model_config = None
        
    def load_model_assets(self) -> None:
        """Load trained model and scalers."""
        artifacts_path = Path(self.config.model_path)
        
        # Load model configuration
        config_file = artifacts_path / "config.json"
        with open(config_file, 'r') as f:
            self.model_config = json.load(f)
        
        # Load scalers
        self.features_scaler = joblib.load(artifacts_path / "features_scaler.pkl")
        self.target_scaler = joblib.load(artifacts_path / "target_scaler.pkl")
        
        # Load model
        self.model = LSTM_Model(
            input_size=self.model_config["input_size"],
            hidden_size=self.model_config["hidden_size"],
            num_layers=self.model_config["num_layers"],
            output_size=self.model_config["output_size"],
        )
        
        state_dict = torch.load(artifacts_path / "lstm_btcusdt_v1.pt", map_location="cpu")
        self.model.load_state_dict(state_dict)
        self.model.eval()
        
        print(f"Loaded model with config: {self.model_config}")
    
    def load_data(self) -> pd.DataFrame:
        """Load and prepare historical data."""
        df = pd.read_csv(self.config.data_file)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df.set_index('timestamp', inplace=True)
        
        # Filter by date range if specified
        if self.config.start_date:
            start_date = pd.to_datetime(self.config.start_date)
            df = df[df.index >= start_date]
        
        if self.config.end_date:
            end_date = pd.to_datetime(self.config.end_date)
            df = df[df.index <= end_date]
        
        # Ensure we have enough data for sequences
        min_required = self.config.sequence_length + 100  # Buffer for training
        if len(df) < min_required:
            raise ValueError(f"Not enough data. Need at least {min_required} rows, got {len(df)}")
        
        print(f"Loaded data: {len(df)} rows from {df.index[0]} to {df.index[-1]}")
        return df
    
    def prepare_features(self, df: pd.DataFrame, end_idx: int) -> Tuple[np.ndarray, float]:
        """Prepare features for prediction at a specific point in time."""
        # Get data up to end_idx (exclusive)
        data_slice = df.iloc[:end_idx].copy()
        
        # Drop first row (RSI NaN) as in training
        data_slice = data_slice.iloc[1:]
        
        # Ensure we have enough data for a sequence
        if len(data_slice) < self.config.sequence_length:
            raise ValueError(f"Not enough data for sequence at index {end_idx}")
        
        # Prepare features and target
        feature_cols = self.model_config["feature_columns"]
        features = data_slice[feature_cols]
        target = data_slice[["close"]]
        
        # Scale features and target
        scaled_features = self.features_scaler.transform(features)
        scaled_target = self.target_scaler.transform(target)
        
        # Create scaled dataframe
        scaled_df = pd.DataFrame(scaled_features, columns=feature_cols, index=features.index)
        scaled_df["close"] = scaled_target
        
        # Get the last sequence
        sequence = scaled_df.iloc[-self.config.sequence_length:].values.astype(np.float32)
        sequence = np.expand_dims(sequence, axis=0)  # Add batch dimension
        
        # Get current close price
        current_close = float(target.iloc[-1, 0])
        
        return sequence, current_close
    
    def predict_next_price(self, sequence: np.ndarray) -> float:
        """Make price prediction using the model."""
        with torch.no_grad():
            sequence_tensor = torch.from_numpy(sequence)
            pred_scaled = self.model(sequence_tensor).numpy()
            pred_price = float(self.target_scaler.inverse_transform(pred_scaled)[0, 0])
        return pred_price
    
    def generate_signal(self, pred_price: float, current_price: float) -> PositionType:
        """Generate trading signal based on prediction."""
        delta = (pred_price - current_price) / current_price
        
        if delta >= self.config.trading.long_threshold:
            return PositionType.LONG
        elif delta <= self.config.trading.short_threshold:
            return PositionType.SHORT
        else:
            return PositionType.FLAT
    
    def run_backtest(self) -> BacktestResults:
        """Run the complete backtest."""
        print("Starting backtest...")
        
        # Load model and data
        self.load_model_assets()
        df = self.load_data()
        
        # Initialize portfolio
        portfolio = Portfolio(self.config.initial_capital)
        
        # Start from a point where we have enough data for sequences
        start_idx = self.config.sequence_length + 1  # +1 for RSI drop
        
        print(f"Running backtest from index {start_idx} to {len(df)}")
        
        # Walk through historical data
        for i in range(start_idx, len(df)):
            current_timestamp = df.index[i]
            current_price = df.iloc[i]['close']
            
            # Update portfolio equity curve
            portfolio.update_equity_curve(current_timestamp, current_price)
            
            # Check risk management rules first
            if portfolio.position is not None:
                should_close = False
                close_reason = ""
                
                # Check stop loss
                if portfolio.should_stop_loss(current_price, self.config.trading.stop_loss_pct):
                    should_close = True
                    close_reason = "stop_loss"
                
                # Check take profit
                elif portfolio.should_take_profit(current_price, self.config.trading.take_profit_pct):
                    should_close = True
                    close_reason = "take_profit"
                
                # Check max holding periods
                elif portfolio.should_close_max_holding(self.config.trading.max_holding_periods):
                    should_close = True
                    close_reason = "max_holding"
                
                if should_close:
                    trade = portfolio.close_position(
                        current_timestamp, current_price,
                        self.config.trading.commission_pct,
                        self.config.trading.slippage_pct
                    )
                    if trade:
                        print(f"Closed position ({close_reason}): {trade.pnl:.2f} ({trade.pnl_pct:.2%})")
            
            # Generate new signal if no position
            if portfolio.position is None:
                try:
                    # Prepare features for prediction
                    sequence, last_close = self.prepare_features(df, i)
                    
                    # Make prediction
                    pred_price = self.predict_next_price(sequence)
                    
                    # Generate signal
                    signal = self.generate_signal(pred_price, last_close)
                    
                    # Execute trade if signal is not FLAT
                    if signal != PositionType.FLAT:
                        can_trade = portfolio.can_open_position(
                            signal, current_price,
                            self.config.trading.position_size_pct,
                            self.config.trading.max_position_size
                        )
                        
                        if can_trade:
                            success = portfolio.open_position(
                                signal, current_timestamp, current_price,
                                self.config.trading.position_size_pct,
                                self.config.trading.commission_pct,
                                self.config.trading.slippage_pct
                            )
                            if success:
                                delta_pct = (pred_price - last_close) / last_close
                                print(f"Opened {signal.value} position at {current_price:.2f} "
                                      f"(pred: {pred_price:.2f}, delta: {delta_pct:.2%})")
                
                except Exception as e:
                    print(f"Error at index {i}: {e}")
                    continue
        
        # Close any remaining position
        if portfolio.position is not None:
            final_price = df.iloc[-1]['close']
            final_timestamp = df.index[-1]
            trade = portfolio.close_position(
                final_timestamp, final_price,
                self.config.trading.commission_pct,
                self.config.trading.slippage_pct
            )
            if trade:
                print(f"Closed final position: {trade.pnl:.2f} ({trade.pnl_pct:.2%})")
        
        # Calculate results
        return self.calculate_results(portfolio, df)
    
    def calculate_results(self, portfolio: Portfolio, df: pd.DataFrame) -> BacktestResults:
        """Calculate comprehensive backtest results."""
        if not portfolio.equity_curve:
            raise ValueError("No equity curve data available")
        
        # Basic metrics
        initial_capital = portfolio.initial_capital
        final_capital = portfolio.equity_curve[-1]
        total_return = (final_capital - initial_capital) / initial_capital
        
        # Calculate daily returns
        equity_series = pd.Series(portfolio.equity_curve, index=portfolio.timestamps)
        daily_returns = equity_series.pct_change().dropna()
        
        # Annual return (assuming 4-hour intervals, ~2190 periods per year)
        periods_per_year = 365 * 24 / 4  # 2190
        total_periods = len(portfolio.equity_curve)
        years = total_periods / periods_per_year
        annual_return = (final_capital / initial_capital) ** (1 / years) - 1 if years > 0 else 0
        
        # Sharpe ratio
        excess_returns = daily_returns - (self.config.risk_free_rate / periods_per_year)
        sharpe_ratio = excess_returns.mean() / excess_returns.std() * np.sqrt(periods_per_year) if excess_returns.std() > 0 else 0
        
        # Maximum drawdown
        peak = np.maximum.accumulate(portfolio.equity_curve)
        drawdown = (np.array(portfolio.equity_curve) - peak) / peak
        max_drawdown = abs(np.min(drawdown))
        
        # Portfolio stats
        portfolio_stats = portfolio.get_summary_stats()
        
        return BacktestResults(
            total_return=total_return,
            annual_return=annual_return,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            win_rate=portfolio_stats["win_rate"],
            profit_factor=portfolio_stats["profit_factor"],
            total_trades=portfolio_stats["total_trades"],
            winning_trades=portfolio_stats["winning_trades"],
            losing_trades=portfolio_stats["losing_trades"],
            avg_win=portfolio_stats["avg_win"],
            avg_loss=portfolio_stats["avg_loss"],
            largest_win=portfolio_stats["largest_win"],
            largest_loss=portfolio_stats["largest_loss"],
            final_capital=final_capital,
            max_capital=max(portfolio.equity_curve),
            min_capital=min(portfolio.equity_curve),
            equity_curve=portfolio.equity_curve,
            trades=[trade.to_dict() for trade in portfolio.trades],
            daily_returns=daily_returns.tolist()
        )
